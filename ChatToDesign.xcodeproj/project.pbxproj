// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		603EEFC92E2DE09A00C449C9 /* AppsFlyerLib-Static in Frameworks */ = {isa = PBXBuildFile; productRef = 603EEFC82E2DE09A00C449C9 /* AppsFlyerLib-Static */; };
		60512F582DB2036C0047E9AC /* FirebaseAppCheck in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E27B2D844F3B006E33C4 /* FirebaseAppCheck */; };
		6052388E2DBA902D00E122FD /* LookinServer in Frameworks */ = {isa = PBXBuildFile; productRef = 6052388D2DBA902D00E122FD /* LookinServer */; };
		60B1E27A2D844F3B006E33C4 /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E2792D844F3B006E33C4 /* FirebaseAnalytics */; };
		60B1E27E2D844F3B006E33C4 /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E27D2D844F3B006E33C4 /* FirebaseAuth */; };
		60B1E2802D844F3B006E33C4 /* FirebaseCore in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E27F2D844F3B006E33C4 /* FirebaseCore */; };
		60B1E2822D844F3B006E33C4 /* FirebaseCrashlytics in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E2812D844F3B006E33C4 /* FirebaseCrashlytics */; };
		60B1E2842D844F3B006E33C4 /* FirebaseInAppMessaging-Beta in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E2832D844F3B006E33C4 /* FirebaseInAppMessaging-Beta */; };
		60B1E2862D844F3B006E33C4 /* FirebaseMessaging in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E2852D844F3B006E33C4 /* FirebaseMessaging */; };
		60B1E2882D844F3B006E33C4 /* FirebasePerformance in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E2872D844F3B006E33C4 /* FirebasePerformance */; };
		60B1E28A2D844F3B006E33C4 /* FirebaseRemoteConfig in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E2892D844F3B006E33C4 /* FirebaseRemoteConfig */; };
		60B1E28D2D844F58006E33C4 /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E28C2D844F58006E33C4 /* Lottie */; };
		60B1E2902D844FB8006E33C4 /* Moya in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E28F2D844FB8006E33C4 /* Moya */; };
		60B1E2E82D8458AE006E33C4 /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E2E72D8458AE006E33C4 /* GoogleSignIn */; };
		60B1E2EA2D8458AE006E33C4 /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E2E92D8458AE006E33C4 /* GoogleSignInSwift */; };
		60B1E3062D849332006E33C4 /* Kingfisher in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E3052D849332006E33C4 /* Kingfisher */; };
		60B1E3082D857A65006E33C4 /* FirebaseFirestore in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E3072D857A65006E33C4 /* FirebaseFirestore */; };
		60B1E30C2D858EBD006E33C4 /* FirebaseStorage in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E30B2D858EBD006E33C4 /* FirebaseStorage */; };
		60B1E30F2D859271006E33C4 /* ExyteChat in Frameworks */ = {isa = PBXBuildFile; productRef = 60B1E30E2D859271006E33C4 /* ExyteChat */; };
		60F010882DFEC709007B85B1 /* RevenueCat in Frameworks */ = {isa = PBXBuildFile; productRef = 60F010872DFEC709007B85B1 /* RevenueCat */; };
		790C3C6B77164BE8AD52421F /* Sentry in Frameworks */ = {isa = PBXBuildFile; productRef = 58F41B14A6204C8F9B42B28D /* Sentry */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		60B1E2672D844E11006E33C4 /* ChatToDesign.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ChatToDesign.app; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		60C56CAB2D86B45C006E4E7E /* Exceptions for "ChatToDesign" folder in "ChatToDesign" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Application/Info.plist,
			);
			target = 60B1E2662D844E11006E33C4 /* ChatToDesign */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		60B1E2692D844E11006E33C4 /* ChatToDesign */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				60C56CAB2D86B45C006E4E7E /* Exceptions for "ChatToDesign" folder in "ChatToDesign" target */,
			);
			path = ChatToDesign;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		60B1E2642D844E11006E33C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				60512F582DB2036C0047E9AC /* FirebaseAppCheck in Frameworks */,
				60B1E28A2D844F3B006E33C4 /* FirebaseRemoteConfig in Frameworks */,
				60B1E27E2D844F3B006E33C4 /* FirebaseAuth in Frameworks */,
				6052388E2DBA902D00E122FD /* LookinServer in Frameworks */,
				60B1E2E82D8458AE006E33C4 /* GoogleSignIn in Frameworks */,
				60B1E2862D844F3B006E33C4 /* FirebaseMessaging in Frameworks */,
				60B1E3082D857A65006E33C4 /* FirebaseFirestore in Frameworks */,
				60B1E2822D844F3B006E33C4 /* FirebaseCrashlytics in Frameworks */,
				60B1E2902D844FB8006E33C4 /* Moya in Frameworks */,
				60B1E28D2D844F58006E33C4 /* Lottie in Frameworks */,
				60B1E2802D844F3B006E33C4 /* FirebaseCore in Frameworks */,
				603EEFC92E2DE09A00C449C9 /* AppsFlyerLib-Static in Frameworks */,
				60B1E3062D849332006E33C4 /* Kingfisher in Frameworks */,
				60F010882DFEC709007B85B1 /* RevenueCat in Frameworks */,
				60B1E2EA2D8458AE006E33C4 /* GoogleSignInSwift in Frameworks */,
				60B1E30C2D858EBD006E33C4 /* FirebaseStorage in Frameworks */,
				60B1E27A2D844F3B006E33C4 /* FirebaseAnalytics in Frameworks */,
				60B1E30F2D859271006E33C4 /* ExyteChat in Frameworks */,
				60B1E2882D844F3B006E33C4 /* FirebasePerformance in Frameworks */,
				60B1E2842D844F3B006E33C4 /* FirebaseInAppMessaging-Beta in Frameworks */,
				790C3C6B77164BE8AD52421F /* Sentry in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		60B1E25E2D844E11006E33C4 = {
			isa = PBXGroup;
			children = (
				60B1E2692D844E11006E33C4 /* ChatToDesign */,
				60B1E2682D844E11006E33C4 /* Products */,
			);
			sourceTree = "<group>";
		};
		60B1E2682D844E11006E33C4 /* Products */ = {
			isa = PBXGroup;
			children = (
				60B1E2672D844E11006E33C4 /* ChatToDesign.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		60B1E2662D844E11006E33C4 /* ChatToDesign */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 60B1E2752D844E13006E33C4 /* Build configuration list for PBXNativeTarget "ChatToDesign" */;
			buildPhases = (
				60B1E2632D844E11006E33C4 /* Sources */,
				60B1E2642D844E11006E33C4 /* Frameworks */,
				60B1E2652D844E11006E33C4 /* Resources */,
				3C95D57BC9924DC2B1373D2F /* Upload Debug Symbols to Sentry */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				60B1E2692D844E11006E33C4 /* ChatToDesign */,
			);
			name = ChatToDesign;
			packageProductDependencies = (
				60B1E2792D844F3B006E33C4 /* FirebaseAnalytics */,
				60B1E27B2D844F3B006E33C4 /* FirebaseAppCheck */,
				60B1E27D2D844F3B006E33C4 /* FirebaseAuth */,
				60B1E27F2D844F3B006E33C4 /* FirebaseCore */,
				60B1E2812D844F3B006E33C4 /* FirebaseCrashlytics */,
				60B1E2832D844F3B006E33C4 /* FirebaseInAppMessaging-Beta */,
				60B1E2852D844F3B006E33C4 /* FirebaseMessaging */,
				60B1E2872D844F3B006E33C4 /* FirebasePerformance */,
				60B1E2892D844F3B006E33C4 /* FirebaseRemoteConfig */,
				60B1E28C2D844F58006E33C4 /* Lottie */,
				60B1E28F2D844FB8006E33C4 /* Moya */,
				58F41B14A6204C8F9B42B28D /* Sentry */,
				60B1E2E72D8458AE006E33C4 /* GoogleSignIn */,
				60B1E2E92D8458AE006E33C4 /* GoogleSignInSwift */,
				60B1E3052D849332006E33C4 /* Kingfisher */,
				60B1E3072D857A65006E33C4 /* FirebaseFirestore */,
				60B1E30B2D858EBD006E33C4 /* FirebaseStorage */,
				60B1E30E2D859271006E33C4 /* ExyteChat */,
				6052388D2DBA902D00E122FD /* LookinServer */,
				60F010872DFEC709007B85B1 /* RevenueCat */,
				603EEFC82E2DE09A00C449C9 /* AppsFlyerLib-Static */,
			);
			productName = ChatToDesign;
			productReference = 60B1E2672D844E11006E33C4 /* ChatToDesign.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		60B1E25F2D844E11006E33C4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					60B1E2662D844E11006E33C4 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 60B1E2622D844E11006E33C4 /* Build configuration list for PBXProject "ChatToDesign" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 60B1E25E2D844E11006E33C4;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				60B1E28B2D844F58006E33C4 /* XCRemoteSwiftPackageReference "lottie-spm" */,
				60B1E28E2D844FB8006E33C4 /* XCRemoteSwiftPackageReference "Moya" */,
				A35379E063BC477396FEDB5D /* XCRemoteSwiftPackageReference "sentry-cocoa" */,
				60B1E2E62D8458AE006E33C4 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
				60B1E3042D849332006E33C4 /* XCRemoteSwiftPackageReference "Kingfisher" */,
				60B1E30D2D859271006E33C4 /* XCRemoteSwiftPackageReference "Chat" */,
				6052388C2DBA902D00E122FD /* XCRemoteSwiftPackageReference "LookinServer" */,
				60F010862DFEC709007B85B1 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */,
				603EEFC72E2DE09A00C449C9 /* XCRemoteSwiftPackageReference "AppsFlyerFramework-Static" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 60B1E2682D844E11006E33C4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				60B1E2662D844E11006E33C4 /* ChatToDesign */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		60B1E2652D844E11006E33C4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3C95D57BC9924DC2B1373D2F /* Upload Debug Symbols to Sentry */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
			);
			name = "Upload Debug Symbols to Sentry";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# This script is responsable to upload debug symbols and source context for Sentry.\nif which sentry-cli >/dev/null; then\nexport SENTRY_ORG=a1dai\nexport SENTRY_PROJECT=chat-to-design\nERROR=$(sentry-cli debug-files upload --include-sources \"$DWARF_DSYM_FOLDER_PATH\" 2>&1 >/dev/null)\nif [ ! $? -eq 0 ]; then\necho \"warning: sentry-cli - $ERROR\"\nfi\nelse\necho \"warning: sentry-cli not installed, download from https://github.com/getsentry/sentry-cli/releases\"\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		60B1E2632D844E11006E33C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		60B1E2732D844E13006E33C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		60B1E2742D844E13006E33C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		60B1E2762D844E13006E33C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ChatToDesign/ChatToDesign.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_ASSET_PATHS = "\"ChatToDesign/Resource/Preview Content\"";
				DEVELOPMENT_TEAM = B46L84PQQ5;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ChatToDesign/Application/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机来拍摄照片和录制视频";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问您的相册来保存设计和图片";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册来选择图片进行上传和分享";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.2.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.a1d.chat-to-design";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		60B1E2772D844E13006E33C4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = ChatToDesign/ChatToDesign.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_ASSET_PATHS = "\"ChatToDesign/Resource/Preview Content\"";
				DEVELOPMENT_TEAM = B46L84PQQ5;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ChatToDesign/Application/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问您的相机来拍摄照片和录制视频";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问您的相册来保存设计和图片";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问您的相册来选择图片进行上传和分享";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.2.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.a1d.chat-to-design";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		60B1E2622D844E11006E33C4 /* Build configuration list for PBXProject "ChatToDesign" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				60B1E2732D844E13006E33C4 /* Debug */,
				60B1E2742D844E13006E33C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		60B1E2752D844E13006E33C4 /* Build configuration list for PBXNativeTarget "ChatToDesign" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				60B1E2762D844E13006E33C4 /* Debug */,
				60B1E2772D844E13006E33C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		603EEFC72E2DE09A00C449C9 /* XCRemoteSwiftPackageReference "AppsFlyerFramework-Static" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/AppsFlyerSDK/AppsFlyerFramework-Static";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 6.17.2;
			};
		};
		6052388C2DBA902D00E122FD /* XCRemoteSwiftPackageReference "LookinServer" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/QMUI/LookinServer/";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.2.8;
			};
		};
		60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.9.0;
			};
		};
		60B1E28B2D844F58006E33C4 /* XCRemoteSwiftPackageReference "lottie-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-spm.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.5.1;
			};
		};
		60B1E28E2D844FB8006E33C4 /* XCRemoteSwiftPackageReference "Moya" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Moya/Moya.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 15.0.3;
			};
		};
		60B1E2E62D8458AE006E33C4 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
		60B1E3042D849332006E33C4 /* XCRemoteSwiftPackageReference "Kingfisher" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/onevcat/Kingfisher.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.3.0;
			};
		};
		60B1E30D2D859271006E33C4 /* XCRemoteSwiftPackageReference "Chat" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/exyte/Chat.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.1;
			};
		};
		60F010862DFEC709007B85B1 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RevenueCat/purchases-ios-spm.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.28.1;
			};
		};
		A35379E063BC477396FEDB5D /* XCRemoteSwiftPackageReference "sentry-cocoa" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/getsentry/sentry-cocoa/";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		58F41B14A6204C8F9B42B28D /* Sentry */ = {
			isa = XCSwiftPackageProductDependency;
			package = A35379E063BC477396FEDB5D /* XCRemoteSwiftPackageReference "sentry-cocoa" */;
			productName = Sentry;
		};
		603EEFC82E2DE09A00C449C9 /* AppsFlyerLib-Static */ = {
			isa = XCSwiftPackageProductDependency;
			package = 603EEFC72E2DE09A00C449C9 /* XCRemoteSwiftPackageReference "AppsFlyerFramework-Static" */;
			productName = "AppsFlyerLib-Static";
		};
		6052388D2DBA902D00E122FD /* LookinServer */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6052388C2DBA902D00E122FD /* XCRemoteSwiftPackageReference "LookinServer" */;
			productName = LookinServer;
		};
		60B1E2792D844F3B006E33C4 /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		60B1E27B2D844F3B006E33C4 /* FirebaseAppCheck */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAppCheck;
		};
		60B1E27D2D844F3B006E33C4 /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		60B1E27F2D844F3B006E33C4 /* FirebaseCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCore;
		};
		60B1E2812D844F3B006E33C4 /* FirebaseCrashlytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCrashlytics;
		};
		60B1E2832D844F3B006E33C4 /* FirebaseInAppMessaging-Beta */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = "FirebaseInAppMessaging-Beta";
		};
		60B1E2852D844F3B006E33C4 /* FirebaseMessaging */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseMessaging;
		};
		60B1E2872D844F3B006E33C4 /* FirebasePerformance */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebasePerformance;
		};
		60B1E2892D844F3B006E33C4 /* FirebaseRemoteConfig */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseRemoteConfig;
		};
		60B1E28C2D844F58006E33C4 /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E28B2D844F58006E33C4 /* XCRemoteSwiftPackageReference "lottie-spm" */;
			productName = Lottie;
		};
		60B1E28F2D844FB8006E33C4 /* Moya */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E28E2D844FB8006E33C4 /* XCRemoteSwiftPackageReference "Moya" */;
			productName = Moya;
		};
		60B1E2E72D8458AE006E33C4 /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2E62D8458AE006E33C4 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		60B1E2E92D8458AE006E33C4 /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2E62D8458AE006E33C4 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		60B1E3052D849332006E33C4 /* Kingfisher */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E3042D849332006E33C4 /* XCRemoteSwiftPackageReference "Kingfisher" */;
			productName = Kingfisher;
		};
		60B1E3072D857A65006E33C4 /* FirebaseFirestore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestore;
		};
		60B1E30B2D858EBD006E33C4 /* FirebaseStorage */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E2782D844F3B006E33C4 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseStorage;
		};
		60B1E30E2D859271006E33C4 /* ExyteChat */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60B1E30D2D859271006E33C4 /* XCRemoteSwiftPackageReference "Chat" */;
			productName = ExyteChat;
		};
		60F010872DFEC709007B85B1 /* RevenueCat */ = {
			isa = XCSwiftPackageProductDependency;
			package = 60F010862DFEC709007B85B1 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCat;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 60B1E25F2D844E11006E33C4 /* Project object */;
}
