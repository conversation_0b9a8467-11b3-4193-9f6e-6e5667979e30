//
//  Feedback.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import Foundation

/// Feedback entity representing user feedback submissions
public struct Feedback: Identifiable, Codable, Equatable {
  /// Unique identifier for the feedback
  public let id: String

  /// User ID who submitted the feedback (optional for anonymous feedback)
  public let userId: String?

  /// Type of feedback
  public let type: FeedbackType

  /// Brief subject/title of the feedback
  public let subject: String

  /// Detailed content of the feedback
  public let content: String

  /// Device information when feedback was submitted
  public let deviceInfo: DeviceInfo

  /// App version when feedback was submitted
  public let appVersion: String

  /// Attached files (screenshots, etc.)
  public let attachments: [FeedbackAttachment]

  /// When the feedback was created
  public let createdAt: Date

  /// Current status of the feedback
  public let status: FeedbackStatus

  /// Initialize a new feedback
  public init(
    id: String,
    userId: String?,
    type: FeedbackType,
    subject: String,
    content: String,
    deviceInfo: DeviceInfo,
    appVersion: String,
    attachments: [FeedbackAttachment],
    createdAt: Date,
    status: FeedbackStatus
  ) {
    self.id = id
    self.userId = userId
    self.type = type
    self.subject = subject
    self.content = content
    self.deviceInfo = deviceInfo
    self.appVersion = appVersion
    self.attachments = attachments
    self.createdAt = createdAt
    self.status = status
  }
}

/// Types of feedback that users can submit
public enum FeedbackType: String, CaseIterable, Codable {
  case bug = "bug"
  case feature = "feature"
  case question = "question"
  case other = "other"

  /// Display name for UI
  public var displayName: String {
    switch self {
    case .bug:
      return "Bug Report"
    case .feature:
      return "Feature Request"
    case .question:
      return "Question"
    case .other:
      return "Other"
    }
  }

  /// Icon name for UI
  public var iconName: String {
    switch self {
    case .bug:
      return "ladybug"
    case .feature:
      return "lightbulb"
    case .question:
      return "questionmark.circle"
    case .other:
      return "ellipsis.circle"
    }
  }
}

/// Feedback attachment representing uploaded files
public struct FeedbackAttachment: Identifiable, Codable, Equatable {
  /// Unique identifier for the attachment
  public let id: String

  /// URL where the attachment is stored
  public let url: String

  /// Original filename
  public let filename: String

  /// Initialize a new feedback attachment
  public init(id: String, url: String, filename: String) {
    self.id = id
    self.url = url
    self.filename = filename
  }
}

/// Device information collected when submitting feedback
public struct DeviceInfo: Codable, Equatable {
  /// Device model (e.g., "iPhone 15 Pro")
  public let deviceModel: String

  /// System version (e.g., "iOS 17.0")
  public let systemVersion: String

  /// App version (e.g., "1.0.0")
  public let appVersion: String

  /// Build number (e.g., "100")
  public let buildNumber: String

  /// Initialize device info
  public init(
    deviceModel: String,
    systemVersion: String,
    appVersion: String,
    buildNumber: String
  ) {
    self.deviceModel = deviceModel
    self.systemVersion = systemVersion
    self.appVersion = appVersion
    self.buildNumber = buildNumber
  }
}

/// Status of feedback submission
public enum FeedbackStatus: Codable, Equatable {
  case draft
  case submitting
  case submitted
  case failed(String)

  /// Check if two FeedbackStatus values are equal
  public static func == (lhs: FeedbackStatus, rhs: FeedbackStatus) -> Bool {
    switch (lhs, rhs) {
    case (.draft, .draft),
      (.submitting, .submitting),
      (.submitted, .submitted):
      return true
    case (.failed(let lhsError), .failed(let rhsError)):
      return lhsError == rhsError
    default:
      return false
    }
  }

  /// Create a failed status from an Error
  public static func failed(error: Error) -> FeedbackStatus {
    return .failed(error.localizedDescription)
  }
}

// MARK: - Factory Methods
extension Feedback {
  /// Create a new feedback with default values
  public static func create(
    userId: String?,
    type: FeedbackType,
    subject: String,
    content: String,
    attachments: [FeedbackAttachment] = []
  ) -> Feedback {
    return Feedback(
      id: UUID().uuidString,
      userId: userId,
      type: type,
      subject: subject,
      content: content,
      deviceInfo: DeviceInfoCollector.collect(),
      appVersion: Bundle.main.appVersion,
      attachments: attachments,
      createdAt: Date(),
      status: .draft
    )
  }
}
