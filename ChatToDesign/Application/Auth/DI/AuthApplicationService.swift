//
//  AuthApplicationService.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/19.
//

import Combine
import Foundation

/// 认证应用服务接口
/// 对外暴露的认证功能接口
public protocol AuthApplicationService {
  /// 认证状态发布者
  var authStatePublisher: AnyPublisher<AuthState, Never> { get }

  /// 获取当前认证状态
  var authState: AuthState { get }

  /// 使用邮箱密码登录
  /// - Parameters:
  ///   - email: 邮箱
  ///   - password: 密码
  func signIn(email: String, password: String) async throws

  /// 使用Google账号登录
  func signInWithGoogle() async throws

  /// 使用Apple账号登录
  func signInWithApple() async throws

  /// 登出
  func signOut() async throws
}
