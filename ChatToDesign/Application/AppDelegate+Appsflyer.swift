//
//  File.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/21.
//


import AppTrackingTransparency
import AppsFlyerLib
import UserNotifications
import UIKit


// MARK: - AppsFlyer Integration


extension AppDelegate {

  /// <PERSON>le app becoming active - start AppsFlyer and request ATT
  func applicationDidBecomeActive(_ application: UIApplication) {
    // Start AppsFlyer SDK
    AppsFlyerLib.shared().start()

    // Request ATT authorization on iOS 14+
    if #available(iOS 14, *) {
      requestATTPermission()
    }
  }

  /// Request App Tracking Transparency permission
  @available(iOS 14, *)
  private func requestATTPermission() {
    ATTrackingManager.requestTrackingAuthorization { status in
      switch status {
      case .authorized:
        Logger.info("ATT: User authorized tracking")
      case .denied:
        Logger.info("ATT: User denied tracking")
      case .notDetermined:
        Logger.info("ATT: Tracking authorization not determined")
      case .restricted:
        Logger.info("ATT: Tracking authorization restricted")
      @unknown default:
        Logger.warning("ATT: Unknown tracking authorization status")
      }

      // Track ATT status for analytics
      let container = AppDependencyContainer.shared
      container.attributionAnalyticsService.track(
        name: "att_status_changed",
        parameters: ["status": self.attStatusString(status)],
        category: .privacy
      )
    }
  }

  /// Convert ATT status to string
  @available(iOS 14, *)
  private func attStatusString(_ status: ATTrackingManager.AuthorizationStatus) -> String {
    switch status {
    case .authorized: return "authorized"
    case .denied: return "denied"
    case .notDetermined: return "not_determined"
    case .restricted: return "restricted"
    @unknown default: return "unknown"
    }
  }

}
