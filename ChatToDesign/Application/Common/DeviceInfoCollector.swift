//
//  DeviceInfoCollector.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import Foundation
import UIKit

/// Utility for collecting device and app information
public struct DeviceInfoCollector {

  /// Collect current device and app information
  /// - Returns: DeviceInfo with current device details
  public static func collect() -> DeviceInfo {
    return DeviceInfo(
      deviceModel: deviceModel(),
      systemVersion: systemVersion(),
      appVersion: Bundle.main.appVersion,
      buildNumber: Bundle.main.buildNumber
    )
  }

  /// Get device model name
  /// - Returns: Human-readable device model name
  private static func deviceModel() -> String {
    var systemInfo = utsname()
    uname(&systemInfo)

    let machineMirror = Mirror(reflecting: systemInfo.machine)
    let identifier = machineMirror.children.reduce("") { identifier, element in
      guard let value = element.value as? Int8, value != 0 else { return identifier }
      return identifier + String(UnicodeScalar(UInt8(value)))
    }

    // Map device identifiers to human-readable names
    return mapDeviceIdentifier(identifier)
  }

  /// Get system version
  /// - Returns: iOS version string
  private static func systemVersion() -> String {
    return "iOS \(UIDevice.current.systemVersion)"
  }

  /// Map device identifier to human-readable name
  /// - Parameter identifier: Device identifier (e.g., "iPhone14,2")
  /// - Returns: Human-readable device name
  private static func mapDeviceIdentifier(_ identifier: String) -> String {
    // Common device mappings
    let deviceMap: [String: String] = [
      // iPhone 15 series
      "iPhone16,1": "iPhone 15",
      "iPhone16,2": "iPhone 15 Plus",
      "iPhone16,3": "iPhone 15 Pro",
      "iPhone16,4": "iPhone 15 Pro Max",

      // iPhone 14 series
      "iPhone15,4": "iPhone 14",
      "iPhone15,5": "iPhone 14 Plus",
      "iPhone15,2": "iPhone 14 Pro",
      "iPhone15,3": "iPhone 14 Pro Max",

      // iPhone 13 series
      "iPhone14,5": "iPhone 13",
      "iPhone14,4": "iPhone 13 mini",
      "iPhone14,2": "iPhone 13 Pro",
      "iPhone14,3": "iPhone 13 Pro Max",

      // iPhone 12 series
      "iPhone13,2": "iPhone 12",
      "iPhone13,1": "iPhone 12 mini",
      "iPhone13,3": "iPhone 12 Pro",
      "iPhone13,4": "iPhone 12 Pro Max",

      // iPhone 11 series
      "iPhone12,1": "iPhone 11",
      "iPhone12,3": "iPhone 11 Pro",
      "iPhone12,5": "iPhone 11 Pro Max",

      // iPhone SE
      "iPhone14,6": "iPhone SE (3rd generation)",
      "iPhone12,8": "iPhone SE (2nd generation)",

      // iPad Pro
      "iPad13,18": "iPad Pro 12.9-inch (6th generation)",
      "iPad13,17": "iPad Pro 12.9-inch (6th generation)",
      "iPad13,16": "iPad Pro 11-inch (4th generation)",
      "iPad13,11": "iPad Pro 11-inch (4th generation)",

      // iPad Air
      "iPad13,1": "iPad Air (4th generation)",
      "iPad13,2": "iPad Air (4th generation)",
      "iPad14,8": "iPad Air (5th generation)",
      "iPad14,9": "iPad Air (5th generation)",

      // iPad
      "iPad12,1": "iPad (9th generation)",
      "iPad12,2": "iPad (9th generation)",

      // iPad mini
      "iPad14,1": "iPad mini (6th generation)",
      "iPad14,2": "iPad mini (6th generation)",

      // Simulator
      "i386": "iPhone Simulator",
      "x86_64": "iPhone Simulator",
      "arm64": "iPhone Simulator",
    ]

    // Return mapped name or fallback to identifier
    return deviceMap[identifier] ?? identifier
  }
}
