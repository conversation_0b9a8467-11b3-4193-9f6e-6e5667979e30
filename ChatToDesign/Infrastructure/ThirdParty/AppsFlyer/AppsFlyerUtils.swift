//
//  AppsFlyerUtils.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/1/21.
//

import Foundation
import AppsFlyerLib

/// Utility functions for AppsFlyer integration
public final class AppsFlyerUtils {
    
    // MARK: - Attribution Data
    
    /// Extract attribution data from AppsFlyer conversion data
    /// - Parameter conversionData: AppsFlyer conversion data
    /// - Returns: Structured attribution data
    public static func extractAttributionData(from conversionData: [AnyHashable: Any]) -> AttributionData {
        var attribution = AttributionData()
        
        // Basic attribution info
        attribution.mediaSource = conversionData["media_source"] as? String
        attribution.campaign = conversionData["campaign"] as? String
        attribution.adGroup = conversionData["adgroup"] as? String
        attribution.adSet = conversionData["adset"] as? String
        attribution.ad = conversionData["ad"] as? String
        attribution.keywords = conversionData["keywords"] as? String
        attribution.channel = conversionData["channel"] as? String
        
        // Install info
        attribution.isFirstLaunch = conversionData["is_first_launch"] as? Bool ?? false
        attribution.installTime = conversionData["install_time"] as? String
        attribution.clickTime = conversionData["click_time"] as? String
        
        // Status
        attribution.afStatus = conversionData["af_status"] as? String
        attribution.afMessage = conversionData["af_message"] as? String
        
        // Custom parameters
        attribution.customParameters = extractCustomParameters(from: conversionData)
        
        return attribution
    }
    
    /// Extract custom parameters from conversion data
    /// - Parameter conversionData: AppsFlyer conversion data
    /// - Returns: Custom parameters dictionary
    private static func extractCustomParameters(from conversionData: [AnyHashable: Any]) -> [String: Any] {
        var customParameters: [String: Any] = [:]
        
        // Known AppsFlyer parameters to exclude
        let knownParameters = Set([
            "media_source", "campaign", "adgroup", "adset", "ad", "keywords", "channel",
            "is_first_launch", "install_time", "click_time", "af_status", "af_message",
            "af_sub1", "af_sub2", "af_sub3", "af_sub4", "af_sub5"
        ])
        
        for (key, value) in conversionData {
            if let stringKey = key as? String, !knownParameters.contains(stringKey) {
                customParameters[stringKey] = value
            }
        }
        
        return customParameters
    }
    
    // MARK: - Revenue Tracking
    
    /// Create revenue event parameters for AppsFlyer
    /// - Parameters:
    ///   - productId: Product identifier
    ///   - price: Product price
    ///   - currency: Currency code
    ///   - quantity: Quantity purchased
    ///   - transactionId: Transaction identifier
    /// - Returns: AppsFlyer revenue parameters
    public static func createRevenueParameters(
        productId: String,
        price: Double,
        currency: String,
        quantity: Int = 1,
        transactionId: String? = nil
    ) -> [String: Any] {
        var parameters: [String: Any] = [
            AFEventParamContentId: productId,
            AFEventParamRevenue: price,
            AFEventParamCurrency: currency,
            AFEventParamQuantity: quantity
        ]
        
        if let transactionId = transactionId {
            parameters["af_receipt_id"] = transactionId
        }
        
        return parameters
    }
    
    // MARK: - User Identification
    
    /// Generate anonymous user ID for AppsFlyer
    /// - Returns: Anonymous user ID
    public static func generateAnonymousUserId() -> String {
        return "anon_\(UUID().uuidString.lowercased())"
    }
    
    /// Validate user ID format for AppsFlyer
    /// - Parameter userId: User ID to validate
    /// - Returns: True if valid
    public static func isValidUserId(_ userId: String?) -> Bool {
        guard let userId = userId, !userId.isEmpty else {
            return false
        }
        
        // AppsFlyer user ID constraints
        let maxLength = 64
        let validCharacters = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "-_@."))
        
        return userId.count <= maxLength &&
               userId.rangeOfCharacter(from: validCharacters.inverted) == nil
    }
    
    // MARK: - Event Validation
    
    /// Validate event name for AppsFlyer
    /// - Parameter eventName: Event name to validate
    /// - Returns: True if valid
    public static func isValidEventName(_ eventName: String) -> Bool {
        guard !eventName.isEmpty else { return false }
        
        let maxLength = 45
        let validCharacters = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "_"))
        
        return eventName.count <= maxLength &&
               eventName.rangeOfCharacter(from: validCharacters.inverted) == nil &&
               eventName.first?.isLetter == true
    }
    
    /// Validate event parameters for AppsFlyer
    /// - Parameter parameters: Parameters to validate
    /// - Returns: Validated parameters
    public static func validateEventParameters(_ parameters: [String: Any]) -> [String: Any] {
        var validatedParameters: [String: Any] = [:]
        
        for (key, value) in parameters {
            // Validate key
            let validatedKey = validateParameterKey(key)
            
            // Validate value
            let validatedValue = validateParameterValue(value)
            
            validatedParameters[validatedKey] = validatedValue
        }
        
        return validatedParameters
    }
    
    /// Validate parameter key
    /// - Parameter key: Parameter key
    /// - Returns: Validated key
    private static func validateParameterKey(_ key: String) -> String {
        let maxLength = 40
        let sanitized = key.lowercased()
            .replacingOccurrences(of: " ", with: "_")
            .filter { $0.isLetter || $0.isNumber || $0 == "_" }
        
        return String(sanitized.prefix(maxLength))
    }
    
    /// Validate parameter value
    /// - Parameter value: Parameter value
    /// - Returns: Validated value
    private static func validateParameterValue(_ value: Any) -> Any {
        switch value {
        case let stringValue as String:
            let maxLength = 1000
            return String(stringValue.prefix(maxLength))
        case let numberValue as NSNumber:
            return numberValue
        case let boolValue as Bool:
            return boolValue
        case let arrayValue as [Any]:
            return arrayValue.prefix(10).map { validateParameterValue($0) }
        default:
            let stringValue = String(describing: value)
            let maxLength = 1000
            return String(stringValue.prefix(maxLength))
        }
    }
    
    // MARK: - Deep Link Utilities
    
    /// Parse deep link URL components
    /// - Parameter url: Deep link URL
    /// - Returns: Parsed components
    public static func parseDeepLinkURL(_ url: URL) -> DeepLinkComponents {
        var components = DeepLinkComponents()
        
        components.scheme = url.scheme
        components.host = url.host
        components.path = url.path
        
        // Parse query parameters
        if let urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: false),
           let queryItems = urlComponents.queryItems {
            var queryParameters: [String: String] = [:]
            for item in queryItems {
                queryParameters[item.name] = item.value
            }
            components.queryParameters = queryParameters
        }
        
        // Parse path components
        let pathComponents = url.pathComponents.filter { $0 != "/" }
        components.pathComponents = pathComponents
        
        return components
    }
    
    // MARK: - Configuration Helpers
    
    /// Get AppsFlyer configuration from environment
    /// - Returns: AppsFlyer configuration
    public static func getConfigurationFromEnvironment() -> AppsFlyerConfiguration? {
        guard let devKey = Bundle.main.object(forInfoDictionaryKey: "AppsFlyerDevKey") as? String,
              let appleAppID = Bundle.main.object(forInfoDictionaryKey: "AppsFlyerAppleAppID") as? String else {
            return nil
        }
        
        let isDebug = Bundle.main.object(forInfoDictionaryKey: "AppsFlyerDebugEnabled") as? Bool ?? false
        
        return AppsFlyerConfiguration(
            devKey: devKey,
            appleAppID: appleAppID,
            isDebugEnabled: isDebug
        )
    }
}

// MARK: - Supporting Data Structures

/// Attribution data extracted from AppsFlyer
public struct AttributionData {
    public var mediaSource: String?
    public var campaign: String?
    public var adGroup: String?
    public var adSet: String?
    public var ad: String?
    public var keywords: String?
    public var channel: String?
    
    public var isFirstLaunch: Bool = false
    public var installTime: String?
    public var clickTime: String?
    
    public var afStatus: String?
    public var afMessage: String?
    
    public var customParameters: [String: Any] = [:]
    
    public init() {}
}

/// Deep link URL components
public struct DeepLinkComponents {
    public var scheme: String?
    public var host: String?
    public var path: String?
    public var queryParameters: [String: String] = [:]
    public var pathComponents: [String] = []
    
    public init() {}
}
