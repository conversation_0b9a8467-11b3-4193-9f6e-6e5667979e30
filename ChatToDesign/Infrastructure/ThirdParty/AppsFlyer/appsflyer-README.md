# AppsFlyer 集成文档

本目录包含 ChatToDesign iOS 应用的完整 AppsFlyer SDK 集成，实现了双分析架构，职责分离：Firebase Analytics（用户行为分析）和 AppsFlyer（营销归因分析）。

## 架构概览

### 双分析架构

- **Firebase Analytics** (`behaviorAnalyticsService`): 用户行为、产品分析、A/B 测试
- **AppsFlyer** (`attributionAnalyticsService`): 营销归因、活动跟踪、ROI 分析

### 核心组件

1. **AppsFlyerAnalyticsAdapter**: 实现 `AnalyticsService` 协议的主要适配器
2. **AppsFlyerConfiguration**: 带验证的配置模型
3. **AppsFlyerEventMapper**: 事件和参数映射工具
4. **AppsFlyerDeepLinkHandler**: 深度链接处理和路由
5. **AppsFlyerUtils**: 实用函数和辅助工具

## 设置说明

### 1. 配置远程配置

在 Firebase Remote Config 中添加 AppsFlyer 凭据：

```
appsflyer_dev_key: "YOUR_APPSFLYER_DEV_KEY"
appsflyer_apple_app_id: "YOUR_APPLE_APP_ID"
appsflyer_debug_enabled: true (开发环境)
```

### 2. 更新 Info.plist

添加 ATT 使用说明：

```xml
<key>NSUserTrackingUsageDescription</key>
<string>此应用使用跟踪来提供个性化广告并衡量广告效果。</string>
```

### 3. 配置 SKAN (iOS 15+)

在 Info.plist 中添加 SKAN 端点：

```xml
<key>NSAdvertisingAttributionReportEndpoint</key>
<string>https://appsflyer-skadnetwork.com/</string>
```

## 使用示例

### 基础事件跟踪

```swift
let container = AppDependencyContainer.shared

// 用户行为 → Firebase Analytics
container.behaviorAnalyticsService.track(
    name: "screen_viewed",
    parameters: ["screen_name": "chat"],
    category: .userBehavior
)

// 营销归因 → AppsFlyer
container.attributionAnalyticsService.track(
    name: "campaign_click",
    parameters: ["campaign": "summer_2024"],
    category: .marketing
)
```

### 智能事件路由

```swift
// 根据事件类别自动路由到合适的服务
container.trackEvent(MarketingAnalyticsEvent.campaignClick(
    campaign: "holiday_sale",
    mediaSource: "facebook"
))
```

### 深度链接处理

```swift
// 监听深度链接路由
NotificationCenter.default.addObserver(
    forName: .deepLinkRouteToChat,
    object: nil,
    queue: .main
) { notification in
    // 处理导航
}
```

### 收入跟踪

```swift
container.attributionAnalyticsService.trackConversion(
    eventName: "purchase",
    value: 9.99,
    currency: "USD",
    parameters: ["product_id": "pro_monthly"]
)
```

## 事件分类

### AppsFlyer 事件（营销归因）

- 活动点击和展示
- 深度链接打开
- 安装归因
- 购买转化
- 收入跟踪
- 重新参与活动

### Firebase Analytics 事件（用户行为）

- 屏幕浏览和导航
- 功能使用
- 用户交互
- 性能指标
- A/B 测试事件
- 错误跟踪

### 双平台事件

- 用户注册/登录
- 关键转化
- 订阅购买

## 深度链接配置

### URL Scheme 设置

在 Info.plist 中配置应用的 URL schemes：

```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.yourapp.deeplink</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>yourapp</string>
        </array>
    </dict>
</array>
```

### 深度链接路由

系统支持基于深度链接值的自动路由：

- `chat` → 导航到聊天界面
- `design` → 导航到设计界面
- `subscription` → 显示付费墙
- `profile` → 导航到个人资料
- `onboarding` → 显示教程

## 隐私合规

### ATT (应用跟踪透明度)

集成自动处理 ATT 请求：

```swift
// ATT 在 AppDelegate.applicationDidBecomeActive 中请求
// 状态变化会被跟踪用于分析
```

### GDPR 合规

配置隐私设置：

```swift
let config = AppsFlyerConfiguration(
    devKey: "your_key",
    appleAppID: "your_id",
    disableIDFACollection: true, // 隐私合规
    anonymousUserMode: true      // 匿名跟踪
)
```

## 测试

### 调试模式

启用调试日志：

```swift
let config = AppsFlyerConfiguration.development(
    devKey: "your_key",
    appleAppID: "your_id"
)
```

### 集成测试

1. 检查 AppsFlyer 控制台中的事件
2. 验证深度链接路由
3. 测试 ATT 流程
4. 验证收入跟踪

## 错误处理

集成包含全面的错误处理：

- 配置验证
- 网络错误恢复
- 优雅降级
- 详细日志记录

## 性能考虑

- 事件自动批处理
- 对应用启动影响最小
- 高效内存使用
- 后台处理

## 故障排除

### 常见问题

1. **事件未显示**: 检查开发者密钥和应用 ID
2. **深度链接不工作**: 验证 URL scheme 配置
3. **ATT 未显示**: 确保 iOS 14+ 和正确的时机
4. **收入未跟踪**: 验证货币和金额格式

### 调试日志

启用调试模式查看详细日志：

```
AppsFlyerAnalyticsAdapter: Event tracked - campaign_click
AppsFlyerDeepLinkHandler: Deep link found - {...}
```

## 迁移指南

如果从单一分析解决方案迁移：

1. 保留现有的 Firebase Analytics 调用
2. 为营销事件添加 AppsFlyer
3. 使用智能路由进行自动分发
4. 逐步迁移到双架构

## 最佳实践

1. **事件命名**: 使用一致、描述性的名称
2. **参数验证**: 始终验证事件参数
3. **隐私优先**: 尊重用户隐私选择
4. **测试**: 测试所有深度链接场景
5. **监控**: 监控两个平台的数据一致性

## 支持

如有问题或疑问：

1. 查看 AppsFlyer 文档
2. 检查集成日志
3. 使用 AppsFlyer 的调试工具测试
4. 如需要可联系 AppsFlyer 支持
