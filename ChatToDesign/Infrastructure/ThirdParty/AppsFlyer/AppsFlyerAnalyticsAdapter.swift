//
//  AppsFlyerAnalyticsAdapter.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/1/21.
//

import AppsFlyerLib
import Foundation

/// AppsFlyer Analytics adapter implementing AnalyticsService protocol
public final class AppsFlyerAnalyticsAdapter: NSObject, AnalyticsService {

  // MARK: - Properties

  private var configuration: AppsFlyerConfiguration?
  private var analyticsConfiguration: AnalyticsConfiguration?
  private var isConfigured = false
  private let logger = Logger.self

  // MARK: - Initialization

  public override init() {
    super.init()
    logger.debug("AppsFlyerAnalyticsAdapter: Initialized")
  }

  // MARK: - AnalyticsService Implementation

  public func configure(with configuration: AnalyticsConfiguration) async {
    self.analyticsConfiguration = configuration

    // AppsFlyer specific configuration should be set separately
    // This method handles general analytics configuration
    logger.info("AppsFlyerAnalyticsAdapter: General analytics configuration applied")
  }

  /// Configure AppsFlyer with specific configuration
  /// - Parameter config: AppsFlyer configuration
  public func configure(with config: AppsFlyerConfiguration) async {
    do {
      try config.validate()
      self.configuration = config

      await configureAppsFlyerSDK(with: config)

      isConfigured = true
      logger.info("AppsFlyerAnalyticsAdapter: Configuration completed successfully")

    } catch {
      logger.error("AppsFlyerAnalyticsAdapter: Configuration failed - \(error)")
      throw error
    }
  }

  public func setAnalyticsCollectionEnabled(_ enabled: Bool) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping collection toggle")
      return
    }

    // AppsFlyer doesn't have a direct equivalent, but we can stop/start the SDK
    if enabled {
      AppsFlyerLib.shared().start()
      logger.info("AppsFlyerAnalyticsAdapter: Analytics collection enabled")
    } else {
//      AppsFlyerLib.shared().stop(true)
      logger.info("AppsFlyerAnalyticsAdapter: Analytics collection disabled")
    }
  }

  // MARK: - Event Tracking

  public func track(event: AnalyticsEvent) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping event tracking")
      return
    }

    // Check if this event should be tracked by AppsFlyer
    guard AppsFlyerEventMapper.shouldTrackEvent(event) else {
      logger.debug(
        "AppsFlyerAnalyticsAdapter: Event '\(event.name)' not relevant for AppsFlyer, skipping")
      return
    }

    let eventName = AppsFlyerEventMapper.mapEventName(event.name)
    let parameters = AppsFlyerEventMapper.mapParameters(event.parameters)

    AppsFlyerLib.shared().logEvent(eventName, withValues: parameters)

    logger.debug(
      "AppsFlyerAnalyticsAdapter: Event tracked - \(eventName), parameters: \(parameters)")
  }

  public func track(events: [AnalyticsEvent]) {
    for event in events {
      track(event: event)
    }
  }

  public func trackScreenView(screenName: String, screenClass: String) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping screen view")
      return
    }

    let parameters: [String: Any] = [
      AFEventParamContentType: "screen",
      AFEventParamContentId: screenName,
      "screen_class": screenClass,
    ]

    AppsFlyerLib.shared().logEvent(AFEventContentView, withValues: parameters)
    logger.debug("AppsFlyerAnalyticsAdapter: Screen view tracked - \(screenName)")
  }

  // MARK: - User Properties

  public func setUserProperties(_ properties: AnalyticsUserProperties) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping user properties")
      return
    }

    for (key, value) in properties.properties {
      setUserProperty(key: key, value: value)
    }

    logger.debug(
      "AppsFlyerAnalyticsAdapter: User properties set - \(properties.properties.count) properties")
  }

  public func setUserId(_ userId: String?) {
    AppsFlyerLib.shared().customerUserID = userId
    logger.debug("AppsFlyerAnalyticsAdapter: User ID set - \(userId ?? "nil")")
  }

  public func setUserProperty(key: String, value: Any?) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping user property")
      return
    }

    // AppsFlyer doesn't have direct user properties like Firebase
    // We can use custom dimensions or additional parameters in events
    let sanitizedKey = AppsFlyerEventMapper.mapParameterKey(key)

    if let value = value {
      // Store for use in future events
      AppsFlyerLib.shared().setAdditionalData([sanitizedKey: value])
      logger.debug("AppsFlyerAnalyticsAdapter: User property set - \(sanitizedKey) = \(value)")
    }
  }

  // MARK: - Session Management

  public func startSession() {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping session start")
      return
    }

    AppsFlyerLib.shared().start()
    logger.debug("AppsFlyerAnalyticsAdapter: Session started")
  }

  public func endSession() {
    // AppsFlyer handles sessions automatically
    logger.debug("AppsFlyerAnalyticsAdapter: Session end (handled automatically)")
  }

  // MARK: - Error Tracking

  public func trackError(_ error: Error, context: [String: Any]?) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping error tracking")
      return
    }

    var parameters: [String: Any] = [
      "error_description": error.localizedDescription,
      "error_domain": (error as NSError).domain,
      "error_code": (error as NSError).code,
    ]

    if let context = context {
      for (key, value) in context {
        parameters["context_\(key)"] = value
      }
    }

    AppsFlyerLib.shared().logEvent("af_error_occurred", withValues: parameters)
    logger.debug("AppsFlyerAnalyticsAdapter: Error tracked - \(error.localizedDescription)")
  }

  public func trackError(code: String, message: String, context: [String: Any]?) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping error tracking")
      return
    }

    var parameters: [String: Any] = [
      "error_code": code,
      "error_message": message,
    ]

    if let context = context {
      for (key, value) in context {
        parameters["context_\(key)"] = value
      }
    }

    AppsFlyerLib.shared().logEvent("af_custom_error", withValues: parameters)
    logger.debug("AppsFlyerAnalyticsAdapter: Custom error tracked - \(code): \(message)")
  }

  // MARK: - Performance Tracking

  public func startPerformanceTrace(name: String, attributes: [String: String]?) -> String? {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping performance trace")
      return nil
    }

    let traceId = UUID().uuidString
    var parameters: [String: Any] = [
      "trace_name": name,
      "trace_id": traceId,
      "trace_action": "start",
    ]

    if let attributes = attributes {
      for (key, value) in attributes {
        parameters["attr_\(key)"] = value
      }
    }

    AppsFlyerLib.shared().logEvent("af_performance_trace", withValues: parameters)
    logger.debug("AppsFlyerAnalyticsAdapter: Performance trace started - \(name)")

    return traceId
  }

  public func stopPerformanceTrace(traceId: String) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping performance trace stop")
      return
    }

    let parameters: [String: Any] = [
      "trace_id": traceId,
      "trace_action": "stop",
    ]

    AppsFlyerLib.shared().logEvent("af_performance_trace", withValues: parameters)
    logger.debug("AppsFlyerAnalyticsAdapter: Performance trace stopped - \(traceId)")
  }

  // MARK: - Conversion Tracking

  public func trackConversion(
    eventName: String,
    value: Double?,
    currency: String?,
    parameters: [String: Any]?
  ) {
    guard isConfigured else {
      logger.warning("AppsFlyerAnalyticsAdapter: Not configured, skipping conversion tracking")
      return
    }

    let mappedEventName = AppsFlyerEventMapper.mapEventName(eventName)
    var conversionParameters: [String: Any] = [:]

    if let value = value {
      conversionParameters[AFEventParamRevenue] = value
    }

    if let currency = currency {
      conversionParameters[AFEventParamCurrency] = currency
    }

    if let parameters = parameters {
      let mappedParams = AppsFlyerEventMapper.mapParameters(parameters)
      conversionParameters.merge(mappedParams) { _, new in new }
    }

    AppsFlyerLib.shared().logEvent(mappedEventName, withValues: conversionParameters)
    logger.debug(
      "AppsFlyerAnalyticsAdapter: Conversion tracked - \(mappedEventName), value: \(value ?? 0)")
  }

  // MARK: - Data Management

  public func flush() {
    // AppsFlyer handles data sending automatically
    logger.debug("AppsFlyerAnalyticsAdapter: Flush requested (handled automatically)")
  }

  public func resetUserData() {
    AppsFlyerLib.shared().customerUserID = nil
    AppsFlyerLib.shared().setAdditionalData([:])
    logger.debug("AppsFlyerAnalyticsAdapter: User data reset")
  }

  public func clearAllData() {
    resetUserData()
    // Note: AppsFlyer doesn't provide a complete data clear method
    // This would require reinstalling the app for complete reset
    logger.debug("AppsFlyerAnalyticsAdapter: All data cleared (partial)")
  }

  // MARK: - Private Configuration Methods

  private func configureAppsFlyerSDK(with config: AppsFlyerConfiguration) async {
    // Set basic configuration
    AppsFlyerLib.shared().appsFlyerDevKey = config.devKey
    AppsFlyerLib.shared().appleAppID = config.appleAppID
    AppsFlyerLib.shared().isDebug = config.isDebugEnabled

    // Set custom user ID if provided
    if let customUserID = config.customUserID {
      AppsFlyerLib.shared().customerUserID = customUserID
    }

    // Configure session settings
      AppsFlyerLib.shared().minTimeBetweenSessions = UInt(config.minTimeBetweenSessions)

    // Configure privacy settings
    if config.disableIDFACollection {
      AppsFlyerLib.shared().disableIDFACollection = true
    }

    if config.disableAppleSearchAds {
      AppsFlyerLib.shared().disableAppleAdSupportTracking = true
    }

    if config.anonymousUserMode {
      AppsFlyerLib.shared().anonymizeUser = true
    }

    // Configure delegates
    if config.conversionDataEnabled {
      AppsFlyerLib.shared().delegate = self
    }

    if config.deepLinkingEnabled {
      AppsFlyerLib.shared().deepLinkDelegate = self
    }

    // Configure ATT support
    if config.attWaitTimeout > 0 {
      AppsFlyerLib.shared().waitForATTUserAuthorization(timeoutInterval: config.attWaitTimeout)
    }

    logger.info(
      "AppsFlyerAnalyticsAdapter: SDK configured with dev key: \(config.devKey.prefix(10))...")
  }
}

// MARK: - AppsFlyerLibDelegate

extension AppsFlyerAnalyticsAdapter: AppsFlyerLibDelegate {

  public func onConversionDataSuccess(_ conversionInfo: [AnyHashable: Any]) {
    logger.info("AppsFlyerAnalyticsAdapter: Conversion data received successfully")
    logger.debug("AppsFlyerAnalyticsAdapter: Conversion data: \(conversionInfo)")

    // Post notification for other parts of the app to handle
    NotificationCenter.default.post(
      name: .appsFlyerConversionDataReceived,
      object: nil,
      userInfo: ["conversionData": conversionInfo]
    )
  }

  public func onConversionDataFail(_ error: Error) {
    logger.error("AppsFlyerAnalyticsAdapter: Conversion data failed - \(error)")

    // Post notification for error handling
    NotificationCenter.default.post(
      name: .appsFlyerConversionDataFailed,
      object: nil,
      userInfo: ["error": error]
    )
  }
}

// MARK: - AppsFlyerDeepLinkDelegate

extension AppsFlyerAnalyticsAdapter: DeepLinkDelegate {

    public func didResolveDeepLink(_ result: DeepLinkResult) {
    logger.info("AppsFlyerAnalyticsAdapter: Deep link resolved")

    switch result.status {
    case .found:
      logger.info(
        "AppsFlyerAnalyticsAdapter: Deep link found - \(result.deepLink?.clickEvent ?? [:])")

      // Post notification for deep link handling
      NotificationCenter.default.post(
        name: .appsFlyerDeepLinkReceived,
        object: nil,
        userInfo: ["deepLinkResult": result]
      )

    case .notFound:
      logger.warning("AppsFlyerAnalyticsAdapter: Deep link not found")

    case .failure:
      logger.error(
        "AppsFlyerAnalyticsAdapter: Deep link resolution failed - \(result.error?.localizedDescription ?? "Unknown error")"
      )

    @unknown default:
      logger.warning("AppsFlyerAnalyticsAdapter: Unknown deep link status")
    }
  }
}

// MARK: - Notification Names

extension Notification.Name {
  public static let appsFlyerConversionDataReceived = Notification.Name(
    "AppsFlyerConversionDataReceived")
  public static let appsFlyerConversionDataFailed = Notification.Name(
    "AppsFlyerConversionDataFailed")
  public static let appsFlyerDeepLinkReceived = Notification.Name("AppsFlyerDeepLinkReceived")
}
