//
//  AppsFlyerUsageExample.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/1/21.
//

import Foundation
import SwiftUI

/// Example usage of AppsFlyer integration
/// This file demonstrates how to use the AppsFlyer services in your ViewModels and Views
public final class AppsFlyerUsageExample {
    
    private let container = AppDependencyContainer.shared
    
    // MARK: - Basic Usage Examples
    
    /// Example: Track user behavior events (goes to Firebase Analytics)
    func trackUserBehaviorExample() {
        // User behavior events go to Firebase Analytics
        container.behaviorAnalyticsService.track(
            name: "screen_viewed",
            parameters: [
                "screen_name": "chat_list",
                "user_id": "user123"
            ],
            category: .userBehavior
        )
        
        container.behaviorAnalyticsService.track(
            name: "feature_used",
            parameters: [
                "feature_name": "ai_chat",
                "session_duration": 120
            ],
            category: .userBehavior
        )
    }
    
    /// Example: Track marketing attribution events (goes to AppsFlyer)
    func trackMarketingAttributionExample() {
        // Marketing events go to AppsFlyer
        container.attributionAnalyticsService.track(
            name: "campaign_click",
            parameters: [
                "campaign": "summer_2024",
                "media_source": "facebook",
                "ad_group": "creative_users"
            ],
            category: .marketing
        )
        
        container.attributionAnalyticsService.track(
            name: "deep_link_opened",
            parameters: [
                "url": "https://app.chattodesign.com/chat?campaign=email",
                "campaign": "email_campaign"
            ],
            category: .deepLink
        )
    }
    
    /// Example: Track conversion events (goes to AppsFlyer)
    func trackConversionExample() {
        // Conversion events for marketing ROI analysis
        container.attributionAnalyticsService.trackConversion(
            eventName: "subscription_purchase",
            value: 9.99,
            currency: "USD",
            parameters: [
                "product_id": "pro_monthly",
                "trial_period": "7_days"
            ]
        )
    }
    
    /// Example: Track critical events to both platforms
    func trackCriticalEventExample() {
        let purchaseEvent = ConversionAnalyticsEvent.purchaseCompleted(
            productId: "pro_annual",
            price: 99.99,
            currency: "USD",
            transactionId: "txn_123456"
        )
        
        // Send to both platforms for comprehensive analysis
        container.trackEventToBoth(purchaseEvent)
    }
    
    // MARK: - Smart Routing Examples
    
    /// Example: Use smart routing based on event category
    func smartRoutingExample() {
        // This will automatically route to the appropriate service
        container.trackEvent(MarketingAnalyticsEvent.campaignClick(
            campaign: "holiday_sale",
            mediaSource: "google_ads"
        ))
        
        container.trackEvent(ConversionAnalyticsEvent.userRegistration(
            method: "email",
            success: true
        ))
        
        container.trackEvent(PrivacyAnalyticsEvent.attStatusChanged(
            status: "authorized"
        ))
    }
    
    // MARK: - Deep Link Handling Examples
    
    /// Example: Handle deep link in ViewModel
    func handleDeepLinkExample() {
        // Listen for deep link notifications
        NotificationCenter.default.addObserver(
            forName: .deepLinkRouteToChat,
            object: nil,
            queue: .main
        ) { notification in
            if let parameters = notification.userInfo?["parameters"] as? DeepLinkParameters {
                self.navigateToChat(with: parameters)
            }
        }
        
        NotificationCenter.default.addObserver(
            forName: .deepLinkRouteToSubscription,
            object: nil,
            queue: .main
        ) { notification in
            if let parameters = notification.userInfo?["parameters"] as? DeepLinkParameters {
                self.showPaywall(with: parameters)
            }
        }
    }
    
    private func navigateToChat(with parameters: DeepLinkParameters) {
        // Handle navigation to chat
        print("Navigating to chat with campaign: \(parameters.campaign ?? "none")")
        
        // Track the deep link conversion
        container.attributionAnalyticsService.track(
            name: "deep_link_conversion",
            parameters: [
                "destination": "chat",
                "campaign": parameters.campaign ?? "organic"
            ],
            category: .conversion
        )
    }
    
    private func showPaywall(with parameters: DeepLinkParameters) {
        // Handle paywall display
        print("Showing paywall with campaign: \(parameters.campaign ?? "none")")
        
        // Track paywall shown from deep link
        container.attributionAnalyticsService.track(
            name: "paywall_shown",
            parameters: [
                "source": "deep_link",
                "campaign": parameters.campaign ?? "organic"
            ],
            category: .conversion
        )
    }
    
    // MARK: - User Lifecycle Examples
    
    /// Example: Track user registration with attribution
    func trackUserRegistrationExample(userId: String, method: String) {
        // Set user ID in both services
        container.behaviorAnalyticsService.setUserId(userId)
        container.attributionAnalyticsService.setUserId(userId)
        
        // Track registration in behavior analytics
        container.behaviorAnalyticsService.track(
            name: "user_signup",
            parameters: [
                "method": method,
                "user_id": userId
            ],
            category: .userBehavior
        )
        
        // Track registration conversion in AppsFlyer
        container.attributionAnalyticsService.track(
            name: "user_registration",
            parameters: [
                "method": method,
                "success": true
            ],
            category: .conversion
        )
    }
    
    /// Example: Track subscription purchase with revenue
    func trackSubscriptionPurchaseExample(
        productId: String,
        price: Double,
        currency: String,
        transactionId: String
    ) {
        // Create revenue parameters
        let revenueParams = AppsFlyerUtils.createRevenueParameters(
            productId: productId,
            price: price,
            currency: currency,
            transactionId: transactionId
        )
        
        // Track in AppsFlyer for marketing ROI
        container.attributionAnalyticsService.track(
            name: "purchase",
            parameters: revenueParams,
            category: .revenue
        )
        
        // Track in Firebase for user behavior analysis
        container.behaviorAnalyticsService.track(
            name: "subscription_purchased",
            parameters: [
                "product_id": productId,
                "price": price,
                "currency": currency
            ],
            category: .userBehavior
        )
    }
}

// MARK: - SwiftUI View Example

/// Example SwiftUI view showing AppsFlyer integration
public struct AppsFlyerExampleView: View {
    @StateObject private var deepLinkHandler = AppDependencyContainer.shared.deepLinkHandler
    @State private var showingPaywall = false
    
    public var body: some View {
        VStack(spacing: 20) {
            Text("AppsFlyer Integration Example")
                .font(.title)
            
            Button("Track Marketing Event") {
                trackMarketingEvent()
            }
            
            Button("Track Conversion Event") {
                trackConversionEvent()
            }
            
            Button("Simulate Deep Link") {
                simulateDeepLink()
            }
            
            if let deepLinkData = deepLinkHandler.deepLinkData {
                VStack {
                    Text("Deep Link Data:")
                        .font(.headline)
                    Text("\(deepLinkData)")
                        .font(.caption)
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
            }
        }
        .padding()
        .onAppear {
            setupDeepLinkHandling()
        }
        .sheet(isPresented: $showingPaywall) {
            PaywallExampleView()
        }
    }
    
    private func trackMarketingEvent() {
        let container = AppDependencyContainer.shared
        container.attributionAnalyticsService.trackCampaignClick(
            campaign: "example_campaign",
            mediaSource: "example_source"
        )
    }
    
    private func trackConversionEvent() {
        let container = AppDependencyContainer.shared
        container.attributionAnalyticsService.trackSubscriptionStarted(
            productId: "example_product",
            price: 9.99,
            currency: "USD"
        )
    }
    
    private func simulateDeepLink() {
        if let url = URL(string: "https://app.chattodesign.com/subscription?campaign=test") {
            deepLinkHandler.processDeepLink(from: url)
        }
    }
    
    private func setupDeepLinkHandling() {
        NotificationCenter.default.addObserver(
            forName: .deepLinkRouteToSubscription,
            object: nil,
            queue: .main
        ) { _ in
            showingPaywall = true
        }
    }
}

/// Example paywall view
private struct PaywallExampleView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack {
            Text("Paywall")
                .font(.title)
            
            Text("Opened from Deep Link")
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Button("Close") {
                dismiss()
            }
            .padding()
        }
        .padding()
    }
}
