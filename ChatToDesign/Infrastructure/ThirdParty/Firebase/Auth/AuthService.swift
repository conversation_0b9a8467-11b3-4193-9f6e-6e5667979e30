import Combine
import Foundation

// MARK: - Interfaces

/// 认证用户接口
public protocol AuthUser {
  var uid: String { get }
  var email: String? { get }
  var isEmailVerified: Bool { get }
  var displayName: String? { get }
  var photoURL: URL? { get }
}

/// 认证状态
public enum AuthState {
  case unauthenticated
  case authenticating
  case authenticated(AuthUser)
  case error(AuthError)
}

/// 认证服务接口
public protocol AuthService {
  // 状态监听
  var authState: AuthState { get }
  var authStatePublisher: AnyPublisher<AuthState, Never> { get }

  // 认证方法
  func signInWithGoogle() async throws -> AuthUser
  func signInWithApple() async throws -> AuthUser
  func signInWithEmail(email: String, password: String) async throws -> AuthUser
  func signOut() throws

  // Token管理
  func getCurrentUserIDToken() async throws -> String?
}

// MARK: - Error Handling

/// 统一错误处理
public enum AuthError: Error, LocalizedError {
  case invalidCredentials
  case userCancelled
  case networkError
  case configurationError
  case unknown(description: String)

  public var errorDescription: String? {
    switch self {
    case .invalidCredentials:
      return "邮箱或密码错误"
    case .userCancelled:
      return "用户取消登录"
    case .networkError:
      return "网络连接错误"
    case .configurationError:
      return "配置错误"
    case .unknown(let desc):
      return "未知错误: \(desc)"
    }
  }
}
