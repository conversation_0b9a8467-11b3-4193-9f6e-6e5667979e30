import AuthenticationServices
import Combine
import CryptoKit
import FirebaseAuth
import FirebaseCore
import GoogleSignIn
import ObjectiveC
import SwiftUI

final class FirebaseAuthAdapter: ObservableObject, AuthService {
  // MARK: - Properties

  private var authStateHandle: AuthStateDidChangeListenerHandle?
  private let auth: Auth
  private var cancellables = Set<AnyCancellable>()

  @Published private(set) var authState: AuthState = .unauthenticated

  var authStatePublisher: AnyPublisher<AuthState, Never> {
    $authState.eraseToAnyPublisher()
  }

  // MARK: - Initialization

  init(auth: Auth = .auth()) {
    self.auth = auth

    // 先检查当前用户状态，再设置监听器
    if let user = auth.currentUser {
      self.authState = .authenticated(FirebaseUserDTO(user: user))
    } else {
      self.authState = .unauthenticated
    }

    setupAuthListener()
  }

  deinit {
    if let handle = authStateHandle {
      auth.removeStateDidChangeListener(handle)
    }
  }

  // MARK: - Private Methods

  private func setupAuthListener() {
    authStateHandle = auth.addStateDidChangeListener { [weak self] _, firebaseUser in
      guard let self = self else { return }

      DispatchQueue.main.async {
        if let user = firebaseUser {
          self.authState = .authenticated(FirebaseUserDTO(user: user))
        } else {
          self.authState = .unauthenticated
        }
      }
    }
  }

  // MARK: - AuthService Implementation

  func signInWithGoogle() async throws -> AuthUser {
    guard let clientID = FirebaseApp.app()?.options.clientID else {
      throw AuthError.configurationError
    }

    let config = GIDConfiguration(clientID: clientID)
    GIDSignIn.sharedInstance.configuration = config

    guard let windowScene = await UIApplication.shared.connectedScenes.first as? UIWindowScene,
      let rootViewController = await windowScene.windows.first?.rootViewController
    else {
      throw AuthError.configurationError
    }

    do {
      let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: rootViewController)

      guard let idToken = result.user.idToken?.tokenString else {
        throw AuthError.unknown(description: "无法获取 Google 身份令牌")
      }

      let accessToken = result.user.accessToken.tokenString
      let credential = GoogleAuthProvider.credential(
        withIDToken: idToken,
        accessToken: accessToken
      )

      let authResult = try await auth.signIn(with: credential)
      return FirebaseUserDTO(user: authResult.user)
    } catch let error as GIDSignInError where error.code == .canceled {
      throw AuthError.userCancelled
    } catch {
      throw AuthError.unknown(description: error.localizedDescription)
    }
  }

  func signInWithApple() async throws -> AuthUser {
    let nonce = randomNonceString()
    let hashedNonce = sha256(nonce)

    let request = ASAuthorizationAppleIDProvider().createRequest()
    request.requestedScopes = [.fullName, .email]
    request.nonce = hashedNonce

    let controller = ASAuthorizationController(authorizationRequests: [request])

    return try await withCheckedThrowingContinuation { continuation in
      Task { @MainActor in
        let delegate = AppleSignInDelegate(continuation: continuation, nonce: nonce, auth: auth)
        controller.delegate = delegate
        controller.presentationContextProvider = delegate

        // Keep a strong reference to the delegate to prevent deallocation
        objc_setAssociatedObject(
          controller, "delegate", delegate, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)

        controller.performRequests()
      }
    }
  }

  func signInWithEmail(email: String, password: String) async throws -> AuthUser {
    do {
      let authResult = try await auth.signIn(withEmail: email, password: password)
      return FirebaseUserDTO(user: authResult.user)
    } catch let error as NSError {
      switch error.code {
      case AuthErrorCode.wrongPassword.rawValue:
        throw AuthError.invalidCredentials
      case AuthErrorCode.networkError.rawValue:
        throw AuthError.networkError
      default:
        throw AuthError.unknown(description: error.localizedDescription)
      }
    }
  }

  func signOut() throws {
    do {
      try auth.signOut()
    } catch {
      throw AuthError.unknown(description: error.localizedDescription)
    }
  }

  func getCurrentUserIDToken() async throws -> String? {
    guard let currentUser = auth.currentUser else {
      return nil
    }

    return try await withCheckedThrowingContinuation { continuation in
      currentUser.getIDTokenForcingRefresh(true) { idToken, error in
        if let error = error {
          continuation.resume(
            throwing: AuthError.unknown(description: "获取ID Token失败: \(error.localizedDescription)"))
          return
        }

        continuation.resume(returning: idToken)
      }
    }
  }

  // MARK: - Apple Sign In Helper Methods

  private func randomNonceString(length: Int = 32) -> String {
    precondition(length > 0)
    let charset: [Character] =
      Array("0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._")
    var result = ""
    var remainingLength = length

    while remainingLength > 0 {
      let randoms: [UInt8] = (0..<16).map { _ in
        var random: UInt8 = 0
        let errorCode = SecRandomCopyBytes(kSecRandomDefault, 1, &random)
        if errorCode != errSecSuccess {
          fatalError(
            "Unable to generate nonce. SecRandomCopyBytes failed with OSStatus \(errorCode)")
        }
        return random
      }

      randoms.forEach { random in
        if remainingLength == 0 {
          return
        }

        if random < charset.count {
          result.append(charset[Int(random)])
          remainingLength -= 1
        }
      }
    }

    return result
  }

  private func sha256(_ input: String) -> String {
    let inputData = Data(input.utf8)
    let hashedData = SHA256.hash(data: inputData)
    let hashString = hashedData.compactMap {
      String(format: "%02x", $0)
    }.joined()

    return hashString
  }
}

// MARK: - Apple Sign In Delegate

private class AppleSignInDelegate: NSObject, ASAuthorizationControllerDelegate,
  ASAuthorizationControllerPresentationContextProviding
{
  private let continuation: CheckedContinuation<AuthUser, Error>
  private let nonce: String
  private let auth: Auth
  private var hasResumed = false

  init(continuation: CheckedContinuation<AuthUser, Error>, nonce: String, auth: Auth) {
    self.continuation = continuation
    self.nonce = nonce
    self.auth = auth
    super.init()
  }

  func authorizationController(
    controller: ASAuthorizationController,
    didCompleteWithAuthorization authorization: ASAuthorization
  ) {
    guard !hasResumed else {
      return
    }

    if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
      guard let appleIDToken = appleIDCredential.identityToken else {
        hasResumed = true
        continuation.resume(
          throwing: AuthError.unknown(description: "Unable to fetch identity token"))
        return
      }

      guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
        hasResumed = true
        continuation.resume(
          throwing: AuthError.unknown(description: "Unable to serialize token string from data"))
        return
      }

      // Create Firebase credential with Apple ID token and full name
      let credential = OAuthProvider.appleCredential(
        withIDToken: idTokenString,
        rawNonce: nonce,
        fullName: appleIDCredential.fullName)

      Task {
        do {
          let authResult = try await auth.signIn(with: credential)

          // Handle display name if not set by Apple credential
          let firebaseUser = authResult.user
          if firebaseUser.displayName?.isEmpty != false {
            let displayName = generateDisplayName(
              from: appleIDCredential.fullName,
              email: firebaseUser.email
            )

            if let displayName = displayName {
              let changeRequest = firebaseUser.createProfileChangeRequest()
              changeRequest.displayName = displayName
              try await changeRequest.commitChanges()
            }
          }

          let user = FirebaseUserDTO(user: authResult.user)
          hasResumed = true
          continuation.resume(returning: user)
        } catch {
          hasResumed = true
          continuation.resume(throwing: AuthError.unknown(description: error.localizedDescription))
        }
      }
    } else {
      hasResumed = true
      continuation.resume(throwing: AuthError.unknown(description: "Unexpected credential type"))
    }
  }

  func authorizationController(
    controller: ASAuthorizationController, didCompleteWithError error: Error
  ) {
    guard !hasResumed else {
      return
    }

    hasResumed = true

    if let authError = error as? ASAuthorizationError, authError.code == .canceled {
      continuation.resume(throwing: AuthError.userCancelled)
    } else {
      continuation.resume(throwing: AuthError.unknown(description: error.localizedDescription))
    }
  }

  // MARK: - Helper Methods

  /// Generate display name from Apple full name or email
  /// - Parameters:
  ///   - fullName: Apple provided full name (PersonNameComponents)
  ///   - email: User's email address
  /// - Returns: Generated display name or nil
  private func generateDisplayName(
    from fullName: PersonNameComponents?,
    email: String?
  ) -> String? {
    // First try to use Apple provided full name
    if let fullName = fullName {
      let formatter = PersonNameComponentsFormatter()
      formatter.style = .default
      let formattedName = formatter.string(from: fullName).trimmingCharacters(
        in: .whitespacesAndNewlines)
      if !formattedName.isEmpty {
        return formattedName
      }
    }

    // Fallback to email prefix if no full name available
    if let email = email, !email.isEmpty {
      let emailPrefix = String(email.split(separator: "@").first ?? "")
      if !emailPrefix.isEmpty {
        return emailPrefix
      }
    }

    return nil
  }

  func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
    guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
      let window = windowScene.windows.first
    else {
      fatalError("No window available for Apple Sign In presentation")
    }
    return window
  }
}
