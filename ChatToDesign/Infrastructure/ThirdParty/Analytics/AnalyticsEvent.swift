// AnalyticsEvent.swift
// ChatToDesign
//
// Created by <PERSON><PERSON><PERSON> on 2025/6/16.
//

import Foundation

// MARK: - Analytics Event Protocol

/// 分析事件协议
public protocol AnalyticsEvent {
  /// 事件名称
  var name: String { get }
  /// 事件参数
  var parameters: [String: Any] { get }
  /// 事件类别
  var category: AnalyticsEventCategory { get }
}

// MARK: - Event Categories

/// 分析事件类别
public enum AnalyticsEventCategory: String, CaseIterable {
  /// 用户行为
  case userBehavior = "user_behavior"
  /// 业务核心
  case businessCore = "business_core"
  /// 转化漏斗
  case conversion = "conversion"
  /// 技术指标
  case technical = "technical"
  /// 错误异常
  case error = "error"

  // MARK: - AppsFlyer 专用类别

  /// 营销归因事件
  case marketing = "marketing"
  /// 收入事件
  case revenue = "revenue"
  /// 隐私事件
  case privacy = "privacy"
  /// 深度链接事件
  case deepLink = "deep_link"
}

// MARK: - Predefined Events

/// 预定义的分析事件
public enum PredefinedAnalyticsEvent {

  // MARK: - User Behavior Events

  /// 用户注册
  case userSignUp(method: String, userId: String)
  /// 用户登录
  case userSignIn(method: String, userId: String)
  /// 用户登出
  case userSignOut(userId: String)
  /// 页面访问
  case screenView(screenName: String, screenClass: String)
  /// 功能使用
  case featureUsed(featureName: String, userId: String?)

  // MARK: - Business Core Events

  /// 聊天创建
  case chatCreated(chatId: String, userId: String)
  /// 消息发送
  case messageSent(chatId: String, messageType: String, userId: String)
  /// 图片生成开始
  case imageGenerationStarted(taskId: String, userId: String, prompt: String?)
  /// 图片生成完成
  case imageGenerationCompleted(
    taskId: String, userId: String, success: Bool, duration: TimeInterval?)

  // MARK: - Conversion Events

  /// 付费墙展示
  case paywallShown(trigger: String, userId: String?)
  /// 订阅购买开始
  case subscriptionPurchaseStarted(productId: String, userId: String)
  /// 订阅购买完成
  case subscriptionPurchaseCompleted(
    productId: String, userId: String, success: Bool, revenue: Double?)
  /// 试用开始
  case trialStarted(productId: String, userId: String)

  // MARK: - Technical Events

  /// 应用启动
  case appLaunched(launchTime: TimeInterval)
  /// 应用进入后台
  case appBackgrounded
  /// 应用进入前台
  case appForegrounded
  /// 网络状态变化
  case networkStatusChanged(status: String)

  // MARK: - Error Events

  /// 错误发生
  case errorOccurred(errorCode: String, errorMessage: String, context: String?)
  /// 崩溃发生
  case crashOccurred(crashId: String, context: String?)
}

// MARK: - AnalyticsEvent Implementation

extension PredefinedAnalyticsEvent: AnalyticsEvent {

  public var name: String {
    switch self {
    // User Behavior
    case .userSignUp: return "user_sign_up"
    case .userSignIn: return "user_sign_in"
    case .userSignOut: return "user_sign_out"
    case .screenView: return "screen_view"
    case .featureUsed: return "feature_used"

    // Business Core
    case .chatCreated: return "chat_created"
    case .messageSent: return "message_sent"
    case .imageGenerationStarted: return "image_generation_started"
    case .imageGenerationCompleted: return "image_generation_completed"

    // Conversion
    case .paywallShown: return "paywall_shown"
    case .subscriptionPurchaseStarted: return "subscription_purchase_started"
    case .subscriptionPurchaseCompleted: return "subscription_purchase_completed"
    case .trialStarted: return "trial_started"

    // Technical
    case .appLaunched: return "app_launched"
    case .appBackgrounded: return "app_backgrounded"
    case .appForegrounded: return "app_foregrounded"
    case .networkStatusChanged: return "network_status_changed"

    // Error
    case .errorOccurred: return "error_occurred"
    case .crashOccurred: return "crash_occurred"
    }
  }

  public var parameters: [String: Any] {
    switch self {
    // User Behavior
    case .userSignUp(let method, let userId):
      return ["method": method, "user_id": userId]
    case .userSignIn(let method, let userId):
      return ["method": method, "user_id": userId]
    case .userSignOut(let userId):
      return ["user_id": userId]
    case .screenView(let screenName, let screenClass):
      return ["screen_name": screenName, "screen_class": screenClass]
    case .featureUsed(let featureName, let userId):
      var params: [String: Any] = ["feature_name": featureName]
      if let userId = userId { params["user_id"] = userId }
      return params

    // Business Core
    case .chatCreated(let chatId, let userId):
      return ["chat_id": chatId, "user_id": userId]
    case .messageSent(let chatId, let messageType, let userId):
      return ["chat_id": chatId, "message_type": messageType, "user_id": userId]
    case .imageGenerationStarted(let taskId, let userId, let prompt):
      var params: [String: Any] = ["task_id": taskId, "user_id": userId]
      if let prompt = prompt { params["prompt"] = prompt }
      return params
    case .imageGenerationCompleted(let taskId, let userId, let success, let duration):
      var params: [String: Any] = ["task_id": taskId, "user_id": userId, "success": success]
      if let duration = duration { params["duration"] = duration }
      return params

    // Conversion
    case .paywallShown(let trigger, let userId):
      var params: [String: Any] = ["trigger": trigger]
      if let userId = userId { params["user_id"] = userId }
      return params
    case .subscriptionPurchaseStarted(let productId, let userId):
      return ["product_id": productId, "user_id": userId]
    case .subscriptionPurchaseCompleted(let productId, let userId, let success, let revenue):
      var params: [String: Any] = ["product_id": productId, "user_id": userId, "success": success]
      if let revenue = revenue { params["revenue"] = revenue }
      return params
    case .trialStarted(let productId, let userId):
      return ["product_id": productId, "user_id": userId]

    // Technical
    case .appLaunched(let launchTime):
      return ["launch_time": launchTime]
    case .appBackgrounded:
      return [:]
    case .appForegrounded:
      return [:]
    case .networkStatusChanged(let status):
      return ["status": status]

    // Error
    case .errorOccurred(let errorCode, let errorMessage, let context):
      var params: [String: Any] = ["error_code": errorCode, "error_message": errorMessage]
      if let context = context { params["context"] = context }
      return params
    case .crashOccurred(let crashId, let context):
      var params: [String: Any] = ["crash_id": crashId]
      if let context = context { params["context"] = context }
      return params
    }
  }

  public var category: AnalyticsEventCategory {
    switch self {
    case .userSignUp, .userSignIn, .userSignOut, .screenView, .featureUsed:
      return .userBehavior
    case .chatCreated, .messageSent, .imageGenerationStarted, .imageGenerationCompleted:
      return .businessCore
    case .paywallShown, .subscriptionPurchaseStarted, .subscriptionPurchaseCompleted, .trialStarted:
      return .conversion
    case .appLaunched, .appBackgrounded, .appForegrounded, .networkStatusChanged:
      return .technical
    case .errorOccurred, .crashOccurred:
      return .error
    }
  }
}

// MARK: - Custom Event

/// 自定义分析事件
public struct CustomAnalyticsEvent: AnalyticsEvent {
  public let name: String
  public let parameters: [String: Any]
  public let category: AnalyticsEventCategory

  public init(name: String, parameters: [String: Any] = [:], category: AnalyticsEventCategory) {
    self.name = name
    self.parameters = parameters
    self.category = category
  }
}
