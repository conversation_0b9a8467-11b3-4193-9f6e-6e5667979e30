//
//  ExplorePageView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/3.
//

import SwiftUI

/// Explore page view - 发现页面
struct ExplorePageView: View {

  // MARK: - Properties

  /// ViewModel 实例
  @StateObject private var viewModel: ExplorePageViewModel

  // MARK: - Initialization

  /// 初始化 ExplorePageView
  /// - Parameter viewModel: ExplorePageViewModel 实例
  init(viewModel: ExplorePageViewModel) {
    self._viewModel = StateObject(wrappedValue: viewModel)
  }

  var body: some View {
    ZStack {
      // Background
      Color(red: 0.035, green: 0.035, blue: 0.043)  // #09090b
        .ignoresSafeArea()

      VStack(spacing: 0) {
        // Navigation Header
        navigationHeader

        // Content Area
        contentArea

        // 底部间距，为底部导航栏留出空间
        Spacer()
          .frame(height: 120)
      }
    }
    .refreshable {
      viewModel.refresh()
    }
    .fullScreenCover(item: $viewModel.selectedDetailItem) { item in
      ExploreItemDetailView(exploreItem: item)
    }
  }

  // MARK: - Navigation Header

  private var navigationHeader: some View {
    HStack {
      // Title
      Text("Explore")
        .font(.custom("Inter", size: 20).weight(.semibold))
        .foregroundColor(.white)

      Spacer()

      // Profile button
      Button(action: {
        // TODO: Handle profile tap
      }) {
        Circle()
          .fill(Color.white.opacity(0.05))
          .frame(width: 40, height: 40)
          .overlay(
            Image(systemName: "person")
              .font(.system(size: 16))
              .foregroundColor(Color(red: 0.443, green: 0.443, blue: 0.478))  // #71717A
          )
      }
    }
    .padding(.horizontal, 24)
    .padding(.top, 8)
  }

  // MARK: - Content Area

  private var contentArea: some View {
    Group {
      if viewModel.isLoading && !viewModel.hasData {
        loadingView
      } else if viewModel.shouldShowErrorState {
        errorView
      } else if viewModel.shouldShowEmptyState {
        emptyStateView
      } else {
        exploreContentView
      }
    }
  }

  // MARK: - Loading View

  private var loadingView: some View {
    VStack(spacing: 16) {
      Spacer()

      ProgressView()
        .scaleEffect(1.2)
        .tint(.white)

      Text("Loading amazing content...")
        .font(.custom("Inter", size: 16))
        .foregroundColor(.white.opacity(0.7))

      Spacer()
    }
  }

  // MARK: - Error View

  private var errorView: some View {
    VStack(spacing: 16) {
      Spacer()

      Image(systemName: "exclamationmark.triangle")
        .font(.system(size: 48))
        .foregroundColor(.red.opacity(0.8))

      Text("Something went wrong")
        .font(.custom("Inter", size: 20).weight(.semibold))
        .foregroundColor(.white)

      Text(viewModel.errorMessage)
        .font(.custom("Inter", size: 14))
        .foregroundColor(.white.opacity(0.7))
        .multilineTextAlignment(.center)
        .padding(.horizontal, 32)

      Button(action: {
        viewModel.refresh()
      }) {
        Text("Try Again")
          .font(.custom("Inter", size: 16).weight(.medium))
          .foregroundColor(.white)
          .padding(.horizontal, 24)
          .padding(.vertical, 12)
          .background(Color.blue)
          .clipShape(Capsule())
      }
      .padding(.top, 8)

      Spacer()
    }
  }

  // MARK: - Empty State View

  private var emptyStateView: some View {
    VStack(spacing: 16) {
      Spacer()

      Image(systemName: "photo.on.rectangle.angled")
        .font(.system(size: 64))
        .foregroundColor(.white.opacity(0.6))

      Text("No content found")
        .font(.custom("Inter", size: 24).weight(.semibold))
        .foregroundColor(.white)

      Text("Try adjusting your filters or check back later for new content")
        .font(.custom("Inter", size: 16))
        .foregroundColor(.white.opacity(0.7))
        .multilineTextAlignment(.center)
        .padding(.horizontal, 32)

      Button(action: {
        viewModel.resetFilters()
      }) {
        Text("Reset Filters")
          .font(.custom("Inter", size: 16).weight(.medium))
          .foregroundColor(.white)
          .padding(.horizontal, 24)
          .padding(.vertical, 12)
          .background(Color.blue)
          .clipShape(Capsule())
      }
      .padding(.top, 8)

      Spacer()
    }
  }

  // MARK: - Explore Content View

  private var exploreContentView: some View {
    ScrollView {
      HStack(alignment: .top, spacing: 16) {
        // Left column
        LazyVStack(spacing: 16) {
          ForEach(Array(viewModel.exploreItems.enumerated()), id: \.element.id) { index, item in
            if index % 2 == 0 {
              ExploreItemCard(item: item, height: masonryHeight(for: index)) {
                viewModel.handleItemTap(item)
              }
            }
          }
        }
        .frame(maxWidth: .infinity)

        // Right column
        LazyVStack(spacing: 16) {
          ForEach(Array(viewModel.exploreItems.enumerated()), id: \.element.id) { index, item in
            if index % 2 == 1 {
              ExploreItemCard(item: item, height: masonryHeight(for: index)) {
                viewModel.handleItemTap(item)
              }
            }
          }
        }
        .frame(maxWidth: .infinity)
      }
      .padding(.horizontal, 24)
      .padding(.top, 16)
    }
  }

  // MARK: - Helper Methods

  /// Generate varying heights for masonry layout
  private func masonryHeight(for index: Int) -> CGFloat {
    let heights: [CGFloat] = [224, 160, 224, 160, 200, 180, 240, 160]
    return heights[index % heights.count]
  }

}
