//
//  AttachmentUploader.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/20.
//

import SwiftUI

/// Component for uploading and managing feedback attachments
struct AttachmentUploader: View {

  /// Reference to the feedback form view model
  @ObservedObject var viewModel: FeedbackFormViewModel

  /// Whether to show image picker
  @State private var showImagePicker = false

  /// Selected image from picker
  @State private var selectedImage: UIImage?

  var body: some View {
    VStack(alignment: .leading, spacing: 12) {
      Text("Add Screenshot (Optional)")
        .font(.custom("Inter", size: 16).weight(.medium))
        .foregroundColor(.primary)

      if viewModel.attachments.isEmpty {
        // Empty state - show add button
        addAttachmentButton
      } else {
        // Show existing attachments
        attachmentsList

        // Show add more button if under limit
        if viewModel.canAddMoreAttachments {
          addMoreButton
        }
      }

      // Upload progress indicator
      if viewModel.isUploadingAttachment {
        uploadProgressView
      }
    }
    .sheet(isPresented: $showImagePicker) {
      ImagePicker(selectedImage: $selectedImage)
    }
    .onChange(of: selectedImage) { image in
      if let image = image {
        Task {
          await viewModel.addImageAttachment(image)
        }
        // Reset the selected image after processing
        selectedImage = nil
      }
    }
  }

  // MARK: - Subviews

  private var addAttachmentButton: some View {
    Button(action: { showImagePicker = true }) {
      HStack(spacing: 12) {
        Image(systemName: "camera")
          .font(.system(size: 16))

        Text("Add Screenshot")
          .font(.custom("Inter", size: 16).weight(.medium))
      }
      .foregroundColor(.blue)
      .padding(.vertical, 12)
      .padding(.horizontal, 16)
      .background(
        RoundedRectangle(cornerRadius: 8)
          .fill(Color.blue.opacity(0.1))
      )
      .overlay(
        RoundedRectangle(cornerRadius: 8)
          .stroke(Color.blue, lineWidth: 1)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }

  private var attachmentsList: some View {
    VStack(spacing: 8) {
      ForEach(viewModel.attachments) { attachment in
        AttachmentRow(
          attachment: attachment,
          onRemove: {
            viewModel.removeAttachment(attachment.id)
          }
        )
      }
    }
  }

  private var addMoreButton: some View {
    Button("Add Another Screenshot") {
      showImagePicker = true
    }
    .font(.custom("Inter", size: 14).weight(.medium))
    .foregroundColor(.blue)
    .padding(.top, 4)
  }

  private var uploadProgressView: some View {
    HStack(spacing: 8) {
      ProgressView()
        .scaleEffect(0.8)

      Text("Uploading...")
        .font(.custom("Inter", size: 14))
        .foregroundColor(.secondary)
    }
    .padding(.top, 4)
  }
}

/// Row displaying a single attachment
private struct AttachmentRow: View {

  let attachment: FeedbackAttachment
  let onRemove: () -> Void

  var body: some View {
    HStack(spacing: 12) {
      // File icon
      Image(systemName: "photo")
        .foregroundColor(.blue)
        .font(.system(size: 16))

      // Filename
      Text(attachment.filename)
        .font(.custom("Inter", size: 14))
        .foregroundColor(.primary)
        .lineLimit(1)

      Spacer()

      // Remove button
      Button(action: onRemove) {
        Image(systemName: "xmark.circle.fill")
          .foregroundColor(.red)
          .font(.system(size: 16))
      }
      .buttonStyle(PlainButtonStyle())
    }
    .padding(.vertical, 8)
    .padding(.horizontal, 12)
    .background(Color(.systemGray6))
    .cornerRadius(8)
  }
}

// MARK: - Preview

#Preview {
  VStack {
    AttachmentUploader(
      viewModel: FeedbackFormViewModel(
        feedbackService: MockFeedbackService(),
        uploadService: MockFileUploadService()
      )
    )
    .padding()

    Spacer()
  }
  .background(Color(.systemBackground))
}

// MARK: - Mock Services for Preview

private class MockFeedbackService: FeedbackService {
  func submitFeedback(_ feedback: Feedback) async throws {
    // Mock implementation
  }
}

private class MockFileUploadService: FileUploadService {
  func uploadFile(data: Data, mimeType: String, fileName: String, prefix: String?) async throws
    -> URL
  {
    return URL(string: "https://example.com/mock.jpg")!
  }

  func uploadImage(image: UIImage, fileName: String, quality: CGFloat, prefix: String?) async throws
    -> URL
  {
    return URL(string: "https://example.com/mock.jpg")!
  }

  func validateFile(data: Data, mimeType: String) throws {
    // Mock implementation
  }
}
