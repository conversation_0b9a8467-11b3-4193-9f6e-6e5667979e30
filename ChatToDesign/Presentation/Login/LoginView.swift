//
//  LoginView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import GoogleSignIn
import SwiftUI

/// 登录视图
/// 提供用户登录界面
struct LoginView: View {
  // MARK: - 状态

  /// 环境对象：认证视图模型
  @EnvironmentObject private var viewModel: AuthViewModel

  /// 用户输入：邮箱
  @State private var email = ""

  /// 用户输入：密码
  @State private var password = ""

  /// 是否显示返回按钮
  @State private var showBackButton = false

  /// 键盘是否可见
  @State private var isKeyboardVisible = false

  /// 输入框焦点状态
  @FocusState private var isEmailFocused: Bool

  @Environment(\.dismiss) private var dismiss

  // MARK: - 视图

  var body: some View {
    ZStack {
      // Background image with overlay
      ZStack {
        // Main background image
        Image("designImage")
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(maxWidth: .infinity, maxHeight: .infinity)
          .clipped()
          .ignoresSafeArea()

        // Dark gradient overlay
        LinearGradient(
          gradient: Gradient(colors: [
            Color.clear,
            Color.black.opacity(0.3),
            Color.black.opacity(0.8),
          ]),
          startPoint: .center,
          endPoint: .bottom
        )
        .ignoresSafeArea()
      }
      .onTapGesture {
        // 点击背景收起键盘
        hideKeyboard()
      }

      VStack(spacing: 0) {
        Spacer()

        // Login controls at bottom
        VStack(spacing: 8) {
          // Social login buttons - 键盘显示时隐藏
          if !isKeyboardVisible {
            HStack(spacing: 16) {
              // Google login button
              Button(action: {
                viewModel.signInWithGoogle()
              }) {
                HStack(spacing: 12) {
                  Image("icon-google")
                    .resizable()
                    .frame(width: 28, height: 28)

                  Text("Google")
                    .font(.custom("Poppins-Medium", size: 18))
                    .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                  RoundedRectangle(cornerRadius: 32)
                    .fill(Color.white.opacity(0.16))
                    .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 32))
                )
              }
              .disabled(viewModel.isLoading)

              // Apple login button
              Button(action: {
                viewModel.signInWithApple()
              }) {
                HStack(spacing: 12) {
                  Image("icon-apple")
                    .resizable()
                    .frame(width: 28, height: 28)

                  Text("Apple")
                    .font(.custom("Poppins-Medium", size: 18))
                    .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                  RoundedRectangle(cornerRadius: 32)
                    .fill(Color.white.opacity(0.16))
                    .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 32))
                )
              }
              .disabled(viewModel.isLoading)
            }

            // OR divider - 键盘显示时隐藏
            HStack {
              Rectangle()
                .fill(Color.white.opacity(0.24))
                .frame(height: 1)

              Text("Or continue with")
                .font(.custom("Poppins-Regular", size: 14))
                .foregroundColor(Color.white.opacity(0.48))
                .padding(.horizontal, 16)

              Rectangle()
                .fill(Color.white.opacity(0.24))
                .frame(height: 1)
            }
            .padding(.vertical, 24)
          }

          // Email input section
          VStack(spacing: 16) {
            // Email input field
            ZStack(alignment: .leading) {
              RoundedRectangle(cornerRadius: 16)
                .fill(Color(hex: "27272a"))
                .frame(height: 56)

              HStack {
                if email.isEmpty {
                  Text("Email")
                    .font(.custom("Geist-Regular", size: 14))
                    .foregroundColor(Color(hex: "a1a1aa"))
                }
                Spacer()
              }
              .padding(.horizontal, 16)

              TextField("", text: $email)
                .font(.custom("Geist-Regular", size: 14))
                .foregroundColor(.white)
                .padding(.horizontal, 16)
                .keyboardType(.emailAddress)
                .autocapitalization(.none)
                .disableAutocorrection(true)
                .focused($isEmailFocused)
            }

            // Continue with email button
            Button(action: {
              // 收起键盘
              hideKeyboard()

              if !email.isEmpty {
                viewModel.signInWithEmail(email: email, password: "dummy_password")
              }
            }) {
              Text("Continue with email")
                .font(.custom("Poppins-Medium", size: 18))
                .foregroundColor(.black)
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                  RoundedRectangle(cornerRadius: 32)
                    .fill(.white)
                )
            }
            .disabled(viewModel.isLoading || email.isEmpty)
          }
          .padding(.bottom, 20)
        }
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
        .offset(y: isKeyboardVisible ? -180 : 0)  // 键盘显示时向上偏移
        .animation(.easeInOut(duration: 0.3), value: isKeyboardVisible)
      }

      // Error message overlay
      if let errorMessage = viewModel.errorMessage {
        VStack {
          Spacer()

          Text(errorMessage)
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.white)
            .multilineTextAlignment(.center)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
              RoundedRectangle(cornerRadius: 8)
                .fill(Color.red.opacity(0.8))
                .background(.ultraThinMaterial)
            )
            .padding(.horizontal, 20)
            .padding(.bottom, 200)

          Spacer()
        }
      }

      // Loading indicator overlay
      if viewModel.isLoading {
        Color.black.opacity(0.3)
          .ignoresSafeArea()

        ProgressView()
          .progressViewStyle(CircularProgressViewStyle(tint: .white))
          .scaleEffect(1.5)
      }
    }
    .navigationBarHidden(true)
    .trackScreenView(ScreenNames.login, screenClass: ScreenClasses.authentication)
    .onAppear {
      setupKeyboardObservers()
    }
  }

  // MARK: - 键盘管理方法

  private func setupKeyboardObservers() {
    NotificationCenter.default.addObserver(
      forName: UIResponder.keyboardWillShowNotification,
      object: nil,
      queue: .main
    ) { _ in
      withAnimation(.easeInOut(duration: 0.3)) {
        isKeyboardVisible = true
      }
    }

    NotificationCenter.default.addObserver(
      forName: UIResponder.keyboardWillHideNotification,
      object: nil,
      queue: .main
    ) { _ in
      withAnimation(.easeInOut(duration: 0.3)) {
        isKeyboardVisible = false
      }
    }
  }

  private func hideKeyboard() {
    isEmailFocused = false
    UIApplication.shared.sendAction(
      #selector(UIResponder.resignFirstResponder),
      to: nil,
      from: nil,
      for: nil
    )
  }
}

// Google 登录按钮样式组件
struct GoogleSignInButton: UIViewRepresentable {
  func makeUIView(context: Context) -> GIDSignInButton {
    let button = GIDSignInButton()
    button.style = .wide
    button.colorScheme = .light
    return button
  }

  func updateUIView(_ uiView: GIDSignInButton, context: Context) {}
}

#Preview {
  LoginView()
    .environmentObject(AuthViewModel())
}
